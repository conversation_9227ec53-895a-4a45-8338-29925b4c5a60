import sys
import os

# 添加当前目录到路径
sys.path.append(os.getcwd())

def run_full_comparison():
    """运行完整的对比测试，模拟您描述的场景"""
    print("="*80)
    print("完整算法对比测试 - Delta参数敏感性分析")
    print("="*80)
    
    # 测试 Robust SOCDA 5.py 完整流程
    print("\n1. 测试 Robust SOCDA 5.py 完整流程")
    try:
        from importlib import import_module
        rs5_module = import_module("Robust SOCDA 5")
        
        # 运行完整测试
        print("运行 delta=1 的情况:")
        # 先将delta设为1
        rs5_module.RobustOGGM.__init__ = lambda self, food_delivery_optimizer: setattr(self, 'delta_for_virtual_driver', 1) or rs5_module.RobustOGGM.__init__.__wrapped__(self, food_delivery_optimizer)
        rs5_module.RobustOGGM.__init__.__wrapped__ = rs5_module.RobustOGGM.__init__
        
        # 修改delta为1并运行测试
        original_init = rs5_module.RobustOGGM.__init__
        def modified_init_1(self, food_delivery_optimizer):
            original_init(self, food_delivery_optimizer)
            self.delta_for_virtual_driver = 1
        rs5_module.RobustOGGM.__init__ = modified_init_1
        
        robust_socda_1, results_1 = rs5_module.test_complete_robust_socda()
        total_cost_1 = results_1[0]['total_robust_cost']  # 取第一个方案的成本
        print(f"RS5 delta=1 总成本: {total_cost_1:.2f}")
        
        # 修改delta为2并运行测试
        def modified_init_2(self, food_delivery_optimizer):
            original_init(self, food_delivery_optimizer)
            self.delta_for_virtual_driver = 2
        rs5_module.RobustOGGM.__init__ = modified_init_2
        
        print("\n运行 delta=2 的情况:")
        robust_socda_2, results_2 = rs5_module.test_complete_robust_socda()
        total_cost_2 = results_2[0]['total_robust_cost']  # 取第一个方案的成本
        print(f"RS5 delta=2 总成本: {total_cost_2:.2f}")
        
        rs5_increase = total_cost_2 - total_cost_1
        rs5_increase_pct = (rs5_increase / total_cost_1) * 100 if total_cost_1 > 0 else 0
        print(f"RS5 成本增加: {rs5_increase:.2f} ({rs5_increase_pct:.1f}%)")
        
        # 恢复原始初始化函数
        rs5_module.RobustOGGM.__init__ = original_init
        
    except Exception as e:
        print(f"RS5测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*50)
    
    # 测试 Robust Hybrid 2.py 完整流程
    print("\n2. 测试 Robust Hybrid 2.py 完整流程")
    try:
        rh2_module = import_module("Robust Hybrid 2")
        
        # 修改delta为1并运行测试
        original_init = rh2_module.OGGM.__init__
        def modified_init_1(self, food_delivery_optimizer):
            original_init(self, food_delivery_optimizer)
            self.delta_for_virtual_driver = 1
        rh2_module.OGGM.__init__ = modified_init_1
        
        print("运行 delta=1 的情况:")
        hybrid_solver_1, results_1 = rh2_module.run_robust_hybrid_analysis()
        total_cost_1 = results_1[0]['total_cost']  # 取第一个方案的成本
        print(f"RH2 delta=1 总成本: {total_cost_1:.2f}")
        
        # 修改delta为2并运行测试
        def modified_init_2(self, food_delivery_optimizer):
            original_init(self, food_delivery_optimizer)
            self.delta_for_virtual_driver = 2
        rh2_module.OGGM.__init__ = modified_init_2
        
        print("\n运行 delta=2 的情况:")
        hybrid_solver_2, results_2 = rh2_module.run_robust_hybrid_analysis()
        total_cost_2 = results_2[0]['total_cost']  # 取第一个方案的成本
        print(f"RH2 delta=2 总成本: {total_cost_2:.2f}")
        
        rh2_increase = total_cost_2 - total_cost_1
        rh2_increase_pct = (rh2_increase / total_cost_1) * 100 if total_cost_1 > 0 else 0
        print(f"RH2 成本增加: {rh2_increase:.2f} ({rh2_increase_pct:.1f}%)")
        
        # 恢复原始初始化函数
        rh2_module.OGGM.__init__ = original_init
        
    except Exception as e:
        print(f"RH2测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*80)
    print("完整对比总结:")
    print("="*80)

if __name__ == "__main__":
    run_full_comparison()
