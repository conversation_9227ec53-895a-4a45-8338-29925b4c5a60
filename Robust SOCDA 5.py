import math
import copy
from typing import List, Dict, Tuple, Any, Set
import numpy as np
from itertools import permutations

# --- RG2 等价鲁棒成本计算函数 -------------------------------------------------
def compute_rg2_robust_cost(sequence, start_pos, start_time, slot,
                             restaurant_coords, customer_coords,
                             order_restaurants,
                             driver_speed, slot_duration,
                             theta_t, deviation_ratio,
                             travel_cost_per_unit, penalty_cost_per_unit,
                             est_delivery_duration, depot_coord,
                             orders_data, stp_interval, current_stp_time, # 新增参数
                             override_first_arc_time: float = None): 
    """(第三次修正) 按照 Robust Gurobi 2.py 的动态规划递推公式精确实现单条路径的鲁棒成本。
    返回 (robust_travel_cost, robust_penalty_cost, total_cost, final_pos, final_time)"""
    if not sequence:
        return 0.0, 0.0, 0.0, start_pos, start_time

    # 1) 构建弧，并正确记录与送货弧关联的订单
    arcs = []
    arc_deliveries: Dict[int, List[int]] = {}
    num_orders = 0
    cur = start_pos
    for node in sequence:
        if node.startswith("pickup_"):
            oid = int(node.split("_")[1])
            rest_id = orders_data[oid]['restaurant_id']
            nxt = restaurant_coords[rest_id]
        elif node.startswith("delivery_"):
            oid = int(node.split("_")[1])
            nxt = customer_coords[oid]
            num_orders += 1
        else:
            continue
        
        arcs.append((cur, nxt))
        arc_idx = len(arcs) - 1

        if node.startswith("delivery_"):
            arc_deliveries.setdefault(arc_idx, []).append(oid)
        
        cur = nxt
    
    # 2) 计算名义时间与偏差
    nominal_times = []
    deviations = []
    
    # 论文逻辑修正: 如果提供了 override_first_arc_time，则第一段弧的时间被强制覆盖
    is_overridden = False
    if override_first_arc_time is not None and arcs:
        is_overridden = True
        first_arc_nom_time = override_first_arc_time
        nominal_times.append(first_arc_nom_time)
        deviations.append(first_arc_nom_time * deviation_ratio)

    for i, (a, b) in enumerate(arcs):
        if is_overridden and i == 0:
            continue # 跳过第一段弧，因为它已被覆盖
        dist = math.hypot(a[0] - b[0], a[1] - b[1])
        t_nom = dist / driver_speed
        nominal_times.append(t_nom)
        deviations.append(t_nom * deviation_ratio)
    
    # 3) Gamma预算 (根据论文[Gamma_t^k = ceil(theta_t * |A^k|)]修正)
    gamma = math.ceil(theta_t * len(arcs)) if arcs else 0

    # 4) 动态规划: dp[i][g] = 完成第i-1条弧,用掉g个预算的最坏时间。增加parent_g用于回溯。
    dp = [[-1.0] * (gamma + 1) for _ in range(len(arcs) + 1)]
    parent_g = [[-1] * (gamma + 1) for _ in range(len(arcs) + 1)] # 回溯路径
    dp[0][0] = start_time

    for i in range(len(arcs)):
        t_nom, dev = nominal_times[i], deviations[i]
        for g in range(gamma + 1):
            # 可能性1: 从dp[i][g]状态到达，当前弧不使用偏差
            path1_time = -1.0
            if dp[i][g] != -1.0:
                path1_time = dp[i][g] + t_nom

            # 可能性2: 从dp[i][g-1]状态到达，当前弧使用偏差
            path2_time = -1.0
            if g > 0 and dp[i][g-1] != -1.0:
                path2_time = dp[i][g-1] + t_nom + dev
            
            if path1_time == -1.0 and path2_time == -1.0:
                continue

            # 新状态是两种可能路径中的最坏情况(最晚时间), 并记录来源
            if path1_time >= path2_time:
                dp[i+1][g] = path1_time
                parent_g[i+1][g] = g
            else:
                dp[i+1][g] = path2_time
                parent_g[i+1][g] = g - 1

    # 5) 基于权重的严格鲁棒目标评估 (与Gurobi模型保持一致)
    # ------------------------------------------------------------------
    # 步骤 1: 计算每条弧对总体目标(旅行 + 延迟)的边际权重 w_i
    deliveries_after_cnt = [0] * len(arcs)
    cnt = 0
    for idx in range(len(arcs)-1, -1, -1):
        if idx in arc_deliveries:
            # 对当前弧之后(含当前弧)如果其终点是送货节点，则计入
            cnt += len(arc_deliveries[idx])
        deliveries_after_cnt[idx] = cnt
    
    weights = []  # w_i = α + β * (#deliveries_after_i)
    for idx in range(len(arcs)):
        w = travel_cost_per_unit + penalty_cost_per_unit * deliveries_after_cnt[idx]
        weights.append(w)

    # 步骤 2: 选择使目标增幅最大的 Γ 条弧 (β_i = 1)
    # 增幅 = w_i * dev_i
    arc_increments = [(weights[i] * deviations[i], i) for i in range(len(arcs))]
    arc_increments.sort(reverse=True, key=lambda x: x[0])
    selected = set(idx for _, idx in arc_increments[:gamma])

    # 步骤 3: 计算被选中的扰动下，各弧的实际行驶时间
    actual_times = []
    for i in range(len(arcs)):
        t = nominal_times[i]
        if i in selected:
            t += deviations[i]
        actual_times.append(t)
    
    # 步骤 4: 计算旅行成本 (严格鲁棒)
    total_travel_time = sum(actual_times)
    robust_travel_cost = total_travel_time * travel_cost_per_unit

    # 步骤 5: 计算每个订单的到达时间与延迟成本 (严格鲁棒)
    robust_penalty_cost = 0.0
    cumulative_time = start_time
    for i in range(len(arcs)):
        cumulative_time += actual_times[i]
        if i in arc_deliveries:
            for oid in arc_deliveries[i]:
                arrival = cumulative_time
                target_time = current_stp_time + est_delivery_duration[oid]
                delay = max(arrival - target_time, 0)
                robust_penalty_cost += delay * penalty_cost_per_unit

    total_cost = robust_travel_cost + robust_penalty_cost
    
    final_service_pos = arcs[-1][1] if arcs else start_pos
    worst_finish_time = start_time + total_travel_time

    return robust_travel_cost, robust_penalty_cost, total_cost, final_service_pos, worst_finish_time
# ---------------------------------------------------------------------------

class RobustOrderGroup:
    """鲁棒订单组类，包含订单序列和相关信息"""
    def __init__(self, orders: List[int], sequence: List[int], starting_order: int):
        self.orders = orders
        self.sequence = sequence
        self.starting_order = starting_order
        self.virtual_driver_state = None
        self.estimated_cost = 0.0
        self.dispatch_cost = 0.0
        self.robust_cost = 0.0
        
    def __len__(self):
        return len(self.orders)
        
    def contains_order(self, order_id: int) -> bool:
        return order_id in self.orders
    
    def get_orders_set(self) -> Set[int]:
        return set(self.orders)

class RobustOGGM:
    """鲁棒订单组生成方法 (Robust Order Group Generation Method)"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.orders_data = food_delivery_optimizer.orders_data
        
        # 提取顾客坐标
        self.customer_coords = {oid: data['customer_coord'] for oid, data in self.orders_data.items()}
        # 提取预期送达时间
        self.estimated_delivery_duration = {oid: data['est_delivery'] for oid, data in self.orders_data.items()}

        self.theta_t = 0.3
        self.travel_time_deviation_ratio = 0.2
        self.delay_threshold = 1.0
        self.stp_interval = food_delivery_optimizer.stp_interval
        self.delta_for_virtual_driver = 2 # 对应论文OGGM步骤2中的δ

        print("Robust OGGM (扩展案例) 初始化完成")
    
    def calculate_distance(self, coord1, coord2):
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def calculate_nominal_travel_time(self, coord1, coord2):
        distance = self.calculate_distance(coord1, coord2)
        return distance / self.driver_speed
    
    def calculate_sequence_robust_cost_budget_constrained(self, sequence, start_pos, start_time, stp, override_first_arc_time: float = None) -> float:
        current_stp_time = stp * self.stp_interval
        return compute_rg2_robust_cost(
            sequence, start_pos, start_time, -1, # slot is not used, pass dummy
            self.restaurant_coords, self.customer_coords, None,
            self.driver_speed, self.slot_duration,
            self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0],
            self.orders_data, self.stp_interval, current_stp_time,
            override_first_arc_time
        )[2]
    
    def get_order_coordinates(self, order_id: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """获取订单的餐厅和顾客坐标"""
        restaurant_id = self.orders_data[order_id]['restaurant_id']
        restaurant_coord = self.restaurant_coords[restaurant_id]
        customer_coord = self.customer_coords[order_id]
        return restaurant_coord, customer_coord
    
    def calculate_single_order_robust_cost(self, order_id: int, driver_start: Tuple[float, float], 
                                         start_time: float, stp: int) -> float:
        sequence = [f"pickup_{order_id}", f"delivery_{order_id}"]
        # 注意: 此处计算单个订单成本时，不应使用override，因为它依赖于特定的真实司机位置
        return self.calculate_sequence_robust_cost_budget_constrained(
            sequence, driver_start, start_time, stp)
    
    def calculate_two_orders_combined_robust_cost(self, order1_id: int, order2_id: int, 
                                                driver_start: Tuple[float, float],
                                                start_time: float, stp: int) -> Tuple[float, float]:
        rest1_coord, cust1_coord = self.get_order_coordinates(order1_id)
        rest2_coord, cust2_coord = self.get_order_coordinates(order2_id)
        
        # 机制1：固定order1的餐厅为第一个访问节点
        sequences_order1_first = [
            [f"pickup_{order1_id}", f"pickup_{order2_id}", f"delivery_{order2_id}", f"delivery_{order1_id}"],
            [f"pickup_{order1_id}", f"pickup_{order2_id}", f"delivery_{order1_id}", f"delivery_{order2_id}"],
            [f"pickup_{order1_id}", f"delivery_{order1_id}", f"pickup_{order2_id}", f"delivery_{order2_id}"]
        ]
        
        costs_order1_first = [self.calculate_sequence_robust_cost_budget_constrained(seq, driver_start, start_time, stp) for seq in sequences_order1_first]
        cost_order1_first = min(costs_order1_first)
        
        # 机制2：固定order2的餐厅为第一个访问节点
        sequences_order2_first = [
            [f"pickup_{order2_id}", f"pickup_{order1_id}", f"delivery_{order1_id}", f"delivery_{order2_id}"],
            [f"pickup_{order2_id}", f"pickup_{order1_id}", f"delivery_{order2_id}", f"delivery_{order1_id}"],
            [f"pickup_{order2_id}", f"delivery_{order2_id}", f"pickup_{order1_id}", f"delivery_{order1_id}"]
        ]
        
        costs_order2_first = [self.calculate_sequence_robust_cost_budget_constrained(seq, driver_start, start_time, stp) for seq in sequences_order2_first]
        cost_order2_first = min(costs_order2_first)
        
        return cost_order1_first, cost_order2_first
    
    def calculate_order_robust_matching_degree(self, order1_id: int, order2_id: int, 
                                             virtual_driver_pos: Tuple[float, float],
                                             start_time: float, stp: int) -> Tuple[float, float]:
        cost1_separate = self.calculate_single_order_robust_cost(order1_id, virtual_driver_pos, start_time, stp)
        cost2_separate = self.calculate_single_order_robust_cost(order2_id, virtual_driver_pos, start_time, stp)
        total_separate_cost = cost1_separate + cost2_separate
        
        cost_order1_first, cost_order2_first = self.calculate_two_orders_combined_robust_cost(
            order1_id, order2_id, virtual_driver_pos, start_time, stp)
        
        fit_o1_o2 = total_separate_cost - cost_order1_first
        fit_o2_o1 = total_separate_cost - cost_order2_first
        
        return fit_o1_o2, fit_o2_o1
    
    def cheapest_insertion_single_order_robust(self, sequence: List[str], new_order_id: int, 
                                             current_start_pos: Tuple[float, float],
                                             current_start_time: float, stp: int,
                                             override_first_arc_time: float = None) -> Tuple[List[str], float]:
        if not sequence:
            new_sequence = [f"pickup_{new_order_id}", f"delivery_{new_order_id}"]
            new_cost = self.calculate_sequence_robust_cost_budget_constrained(
                new_sequence, current_start_pos, current_start_time, stp,
                override_first_arc_time=override_first_arc_time)
            return new_sequence, new_cost
        
        best_sequence = None
        best_cost = float('inf')
        
        pickup_node = f"pickup_{new_order_id}"
        delivery_node = f"delivery_{new_order_id}"
        
        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                temp_sequence = sequence[:]
                temp_sequence.insert(pickup_pos, pickup_node)
                temp_sequence.insert(delivery_pos, delivery_node)
                
                cost = self.calculate_sequence_robust_cost_budget_constrained(
                    temp_sequence, current_start_pos, current_start_time, stp,
                    override_first_arc_time=override_first_arc_time)
                
                if cost < best_cost:
                    best_cost = cost
                    best_sequence = temp_sequence
        
        base_cost = self.calculate_sequence_robust_cost_budget_constrained(
            sequence, current_start_pos, current_start_time, stp,
            override_first_arc_time=override_first_arc_time)
        
        return best_sequence, best_cost - base_cost
    
    def calculate_max_delay_in_sequence(self, sequence: List[str], start_time: float, 
                                        start_pos: Tuple[float, float], stp: int,
                                        override_first_arc_time: float = None) -> float:
        """(第二次修正) 计算给定序列在鲁棒场景下的最大订单延迟。
        该实现针对每个交付点，独立计算其最坏情况下的到达时间，以得到真实的最大延迟。
        """
        if not sequence:
            return 0.0

        current_stp_time = stp * self.stp_interval
        
        # 1) 构建弧、名义时间、偏差等基础信息
        arcs = []
        arc_deliveries: Dict[int, List[int]] = {}
        cur = start_pos if start_pos else (0,0)
        
        for node in sequence:
            if node.startswith("pickup_"):
                oid = int(node.split("_")[1])
                rest_id = self.orders_data[oid]['restaurant_id']
                nxt = self.restaurant_coords[rest_id]
            elif node.startswith("delivery_"):
                oid = int(node.split("_")[1])
                nxt = self.customer_coords[oid]
            else: continue
            
            arcs.append((cur, nxt))
            if node.startswith("delivery_"):
                arc_deliveries.setdefault(len(arcs) - 1, []).append(oid)
            cur = nxt
            
        nominal_times, deviations = [], []
        is_overridden = False
        if override_first_arc_time is not None and arcs:
            is_overridden = True
            nominal_times.append(override_first_arc_time)
            deviations.append(override_first_arc_time * self.travel_time_deviation_ratio)

        for i, (a, b) in enumerate(arcs):
            if is_overridden and i == 0: continue
            dist = math.hypot(a[0] - b[0], a[1] - b[1])
            t_nom = dist / self.driver_speed
            nominal_times.append(t_nom)
            deviations.append(t_nom * self.travel_time_deviation_ratio)

        if not arcs: return 0.0
            
        # 2) 针对每个交付点，计算其最坏情况下的延迟
        max_delay = 0.0
        max_delay_ratio = 0.0
        
        cumulative_nom_times = [0.0] * len(arcs)
        cumulative_nom_times[0] = nominal_times[0]
        for i in range(1, len(arcs)):
            cumulative_nom_times[i] = cumulative_nom_times[i-1] + nominal_times[i]

        for i in range(len(arcs)):
            if i in arc_deliveries:
                # 这是一个交付弧。计算其最坏情况下的到达时间。
                # 考虑从起点到当前弧i的子路径
                sub_path_deviations = deviations[:i+1]
                
                # 计算子路径的Gamma预算
                gamma_sub = math.ceil(self.theta_t * len(sub_path_deviations))
                
                # 将预算分配给子路径中最大的偏差
                sub_path_deviations.sort(reverse=True)
                worst_case_perturbation = sum(sub_path_deviations[:gamma_sub])
                
                # 计算最坏到达时间
                worst_case_arrival = start_time + cumulative_nom_times[i] + worst_case_perturbation
                
                # 检查此交付点交付的所有订单的延迟
                for oid in arc_deliveries[i]:
                    target_time = current_stp_time + self.estimated_delivery_duration[oid]
                    delay = max(worst_case_arrival - target_time, 0)
                    if delay > max_delay:
                        max_delay = delay
                    # 计算延迟度 (J.5): νe_i = max(delay,0)/(σ_i- p̃_i) = delay / est_delivery_duration
                    est_dur = self.estimated_delivery_duration[oid]
                    if est_dur > 0:
                        delay_ratio = delay / est_dur
                        if delay_ratio > max_delay_ratio:
                            max_delay_ratio = delay_ratio
        
        return max_delay_ratio

    def calculate_max_robust_delay_degree_in_sequence(self, sequence: List[str], start_time: float,
                                                    start_pos: Tuple[float, float]) -> float:
        # 使用start_time作为当前STP时间的近似
        _, _, _, final_pos, final_time = compute_rg2_robust_cost(
            sequence, start_pos, start_time, -1,
            self.restaurant_coords, self.customer_coords, None,
            self.driver_speed, self.slot_duration, self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0],
            self.orders_data, self.stp_interval, start_time)
        
        max_delay_degree = 0.0
        # Re-calculate to get individual delays
        # This is a simplification; a full delay calculation is complex
        # For now, we rely on the total penalty from the cost function as a proxy
        return 0.0 # Simplified for now
    
    def generate_robust_order_groups_for_stp(self, orders_in_stp: List[int], stp: int,
                                            driver_states: Dict) -> List[RobustOrderGroup]:
        print(f"\n=== 开始为STP {stp}生成鲁棒订单组 ===")
        print(f"STP {stp}订单: {orders_in_stp}")

        if not orders_in_stp:
            return []
        
        # 预先缓存 pickup 坐标，避免重复计算
        pickup_coords_cache = {
            oid: self.restaurant_coords[self.orders_data[oid]['restaurant_id']]
            for oid in orders_in_stp
        }

        all_order_groups = []
        for starting_order in orders_in_stp:
            # ---- OGGM步骤2: 为当前起始订单 o_i 确定虚拟司机 k'_i ----
            pick_coord = pickup_coords_cache[starting_order]

            # 2a. 找到 δ 个最近的真实司机
            driver_distances = []
            for did, state in driver_states.items():
                dist = self.calculate_distance(state['pos'], pick_coord)
                driver_distances.append({'did': did, 'state': state, 'dist': dist})
            
            driver_distances.sort(key=lambda x: x['dist'])
            num_drivers_to_consider = min(self.delta_for_virtual_driver, len(driver_distances))
            closest_drivers = driver_distances[:num_drivers_to_consider]

            # 2b. 计算平均距离和平均时间 (严格遵循论文)
            avg_dist_to_pickup = sum(d['dist'] for d in closest_drivers) / len(closest_drivers) if closest_drivers else 0
            v_start_time = sum(d['state']['time'] for d in closest_drivers) / len(closest_drivers) if closest_drivers else list(driver_states.values())[0]['time']
            override_time = avg_dist_to_pickup / self.driver_speed

            # ---- OGGM步骤5-8: 迭代生成订单组 ----
            current_order_cluster = {starting_order}
            current_sequence = [f"pickup_{starting_order}", f"delivery_{starting_order}"]
            
            # 为起始订单本身创建的第一个订单组，成本计算需要使用override_time
            current_cost = self.calculate_sequence_robust_cost_budget_constrained(
                current_sequence, None, v_start_time, stp, override_first_arc_time=override_time
            )
            initial_rog = RobustOrderGroup([starting_order], current_sequence[:], starting_order)
            initial_rog.robust_cost = current_cost
            all_order_groups.append(initial_rog)
            
            remaining_orders = set(orders_in_stp) - {starting_order}
            
            while remaining_orders and len(current_order_cluster) < self.max_orders_per_driver:
                # ---- OGGM步骤5: 计算当前订单组 B 与每个剩余订单 o_i 的匹配度 fit(B, o_i) ----
                order_matching_scores = []
                for order_id in remaining_orders:
                    # fit = C(B) + C(o) - C(B U o)
                    # C(B) 是 current_cost
                    
                    # 计算 C(o): 单独服务该订单的成本, 必须从同一虚拟司机视角出发
                    # 这意味着第一段路程也是 override_time
                    seq_o = [f"pickup_{order_id}", f"delivery_{order_id}"]
                    cost_o = self.calculate_sequence_robust_cost_budget_constrained(
                        seq_o, None, v_start_time, stp, override_first_arc_time=override_time)
                    
                    # 计算 C(B U o): 合并服务后的成本
                    _, cost_increase = self.cheapest_insertion_single_order_robust(
                        current_sequence, order_id, None, v_start_time, stp,
                        override_first_arc_time=override_time)
                    cost_combined = current_cost + cost_increase

                    fit = current_cost + cost_o - cost_combined
                    order_matching_scores.append((order_id, fit))

                # ---- OGGM步骤5续: 排序形成 L_ε ----
                order_matching_scores.sort(key=lambda x: x[1], reverse=True)
                
                if not order_matching_scores or order_matching_scores[0][1] <= 0:
                    break # 如果没有正向匹配或没有可选订单，则停止为当前起始订单生成更大的组
                
                # ---- OGGM步骤6 & 9: 检查延迟度并选择最优订单 ----
                best_order_to_add = None
                best_new_sequence = None
                
                for candidate_order, fit_value in order_matching_scores:
                    # 首先，生成合并后的临时序列
                    temp_sequence, _ = self.cheapest_insertion_single_order_robust(
                        current_sequence, candidate_order, None, v_start_time, stp,
                        override_first_arc_time=override_time)
                    
                    # 然后，计算这个临时序列的最大延迟度
                    max_delay_ratio = self.calculate_max_delay_in_sequence(
                        temp_sequence, v_start_time, None, stp, 
                        override_first_arc_time=override_time)
                        
                    # 只有当延迟度满足阈值时，才接受该候选订单
                    if max_delay_ratio <= self.delay_threshold:
                        best_order_to_add = candidate_order
                        best_new_sequence = temp_sequence
                        break # 找到第一个满足条件的就跳出
                
                # 如果所有高匹配度的订单都不满足延迟约束，则停止扩展
                if best_order_to_add is None:
                    break

                # ---- OGGM步骤7-8: 确认合并 ----
                current_order_cluster.add(best_order_to_add)
                current_sequence = best_new_sequence
                remaining_orders.remove(best_order_to_add)
                
                current_cost = self.calculate_sequence_robust_cost_budget_constrained(
                    current_sequence, None, v_start_time, stp, override_first_arc_time=override_time
                )
                new_rog = RobustOrderGroup(list(current_order_cluster), current_sequence[:], starting_order)
                new_rog.robust_cost = current_cost
                all_order_groups.append(new_rog)
        
        print(f"STP {stp}生成{len(all_order_groups)}个订单组")
        unique_order_groups = []
        seen_order_sets = set()
        for rog in sorted(all_order_groups, key=lambda x: (len(x.orders), tuple(sorted(x.orders)))):
            orders_tuple = tuple(sorted(rog.orders))
            if orders_tuple not in seen_order_sets:
                unique_order_groups.append(rog)
                seen_order_sets.add(orders_tuple)
        
        print(f"去重后剩余 {len(unique_order_groups)} 个。")
        return unique_order_groups

class RobustOGSA:
    """鲁棒订单组选择算法 (Robust Order Group Selection Algorithm)"""
    
    def __init__(self, food_delivery_optimizer, robust_oggm):
        self.optimizer = food_delivery_optimizer
        self.robust_oggm = robust_oggm # 需要OGGM来重新计算成本
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.orders_data = food_delivery_optimizer.orders_data
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = {oid: data['customer_coord'] for oid, data in self.orders_data.items()}
        self.estimated_delivery_duration = {oid: data['est_delivery'] for oid, data in self.orders_data.items()}

        self.theta_t = 0.3
        self.travel_time_deviation_ratio = 0.2
        self.epsilon = 1.0
        self.delta_max = 3 # 对应论文OGSA和OGAH附录中描述的循环上限
        self.stp_interval = food_delivery_optimizer.stp_interval

        print("Robust OGSA (扩展案例) 初始化完成")
    
    def calculate_evaluation_function(self, dispatch_cost: float, num_orders: int) -> float:
        if num_orders <= 0: return float('inf')
        return dispatch_cost / (num_orders ** self.epsilon)
    
    def select_robust_order_groups_for_stp(self, order_groups: List[RobustOrderGroup], 
                                           all_orders: List[int], stp: int,
                                           driver_states: Dict) -> Dict[int, List[RobustOrderGroup]]:
        print(f"\n=== 开始Robust OGSA为STP {stp}选择订单组 ===")
        if not order_groups or not all_orders: return {}
        
        all_selections = {}

        # 附录K, 步骤2: 遍历 delta 从 1 到 delta_max
        for delta in range(1, self.delta_max + 1):
            print(f"\n--- OGSA: 评估视角 delta = {delta} ---")
            
            # 附录K, 步骤3-4: 为每个OG基于当前delta重新估算成本
            evaluated_ogs = []
            for og in order_groups:
                if not og.sequence: continue

                # 获取第一个访问节点坐标 (附录K, 步骤3)
                first_node_str = og.sequence[0]
                oid = int(first_node_str.split("_")[1])
                first_node_coord = self.restaurant_coords[self.orders_data[oid]['restaurant_id']]
                
                # 找到 delta 个最近的真实司机
                driver_distances = []
                for did, state in driver_states.items():
                    dist = math.hypot(state['pos'][0] - first_node_coord[0], state['pos'][1] - first_node_coord[1])
                    driver_distances.append({'state': state, 'dist': dist})
                
                driver_distances.sort(key=lambda x: x['dist'])
                
                num_drivers_for_avg = min(delta, len(driver_distances))
                if num_drivers_for_avg == 0: continue
                closest_drivers = driver_distances[:num_drivers_for_avg]

                # 计算虚拟司机状态 (附录K, 步骤3)
                avg_start_time = sum(d['state']['time'] for d in closest_drivers) / len(closest_drivers)
                avg_dist_to_pickup = sum(d['dist'] for d in closest_drivers) / len(closest_drivers)
                override_time = avg_dist_to_pickup / self.driver_speed

                # 使用 OGGM 的成本计算函数重新计算成本 (附录K, 步骤4)
                cost = self.robust_oggm.calculate_sequence_robust_cost_budget_constrained(
                    og.sequence, 
                    None, # start_pos is irrelevant due to override_time
                    avg_start_time, 
                    stp, 
                    override_first_arc_time=override_time
                )

                # 附录K, 步骤5: 计算评价值
                eval_val = self.calculate_evaluation_function(cost, len(og))
                evaluated_ogs.append({'og': og, 'cost': cost, 'eval': eval_val})

            # 贪心集合覆盖启发式
            remaining_ogs_to_cover = copy.deepcopy(evaluated_ogs)
            uncovered_orders = set(all_orders)
            current_selection = []

            while uncovered_orders and remaining_ogs_to_cover:
                # 筛选可以覆盖剩余订单的OG
                candidate_ogs = [e_og for e_og in remaining_ogs_to_cover if not e_og['og'].get_orders_set().isdisjoint(uncovered_orders)]
                
                if not candidate_ogs:
                    break
                
                # 附录K, 步骤6: 找到评价值最低的OG
                candidate_ogs.sort(key=lambda x: x['eval'])
                best_evaluated_og = candidate_ogs[0]
                best_og = best_evaluated_og['og']
                
                current_selection.append(best_og)
                
                # 附录K, 步骤7: 更新
                uncovered_orders -= best_og.get_orders_set()
                selected_orders_set = best_og.get_orders_set()
                remaining_ogs_to_cover = [e_og for e_og in remaining_ogs_to_cover if e_og['og'].get_orders_set().isdisjoint(selected_orders_set)]
            
            # 存储当前 delta 的方案
            if not uncovered_orders:
                all_selections[delta] = current_selection
                print(f"--- 视角 delta={delta}: 成功覆盖所有订单，选择了 {len(current_selection)} 个订单组 ---")
            else:
                all_selections[delta] = current_selection # 即使未完全覆盖也记录
                print(f"--- 视角 delta={delta}: 警告, 未能完全覆盖所有订单，还有 {len(uncovered_orders)} 个未覆盖 ---")

        return all_selections

class RobustDriver:
    """鲁棒司机状态类"""
    def __init__(self, driver_id: int, start_pos: Tuple[float, float], start_time: float, capacity: int):
        self.driver_id = driver_id
        self.initial_pos = start_pos
        self.initial_time = start_time
        self.current_pos = start_pos
        self.current_time = start_time
        self.remaining_capacity = capacity
        self.assigned_orders = []
        self.sequence = []
        self.total_robust_cost = 0.0
        
    def can_handle_order_group(self, order_group) -> bool:
        return self.remaining_capacity >= len(order_group)
    
    def assign_order_group(self, order_group, cost_details: Dict):
        """(已修改) 使用包含完整路径信息的字典来更新司机状态"""
        self.assigned_orders.extend(order_group.orders)
        self.remaining_capacity -= len(order_group)
        self.total_robust_cost = cost_details['total_cost']
        self.current_pos = cost_details['final_pos']
        self.current_time = cost_details['final_time']
        self.sequence = cost_details['final_sequence']

class RobustOGAH:
    """鲁棒订单组分配启发式算法 (Robust Order Group Assignment Heuristic)"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.orders_data = food_delivery_optimizer.orders_data
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = {oid: data['customer_coord'] for oid, data in self.orders_data.items()}
        self.estimated_delivery_duration = {oid: data['est_delivery'] for oid, data in self.orders_data.items()}
        self.num_drivers = food_delivery_optimizer.num_drivers
        
        self.theta_t = 0.3
        self.travel_time_deviation_ratio = 0.2
        self.stp_interval = food_delivery_optimizer.stp_interval
        self.delta_max = 3 # OGAH附录中描述的循环上限

        print("Robust OGAH (扩展案例) 初始化完成")
    
    def calculate_robust_driver_dispatch_cost(self, order_group, driver: RobustDriver, stp: int):
        """(已修改) 计算分配一个订单组给司机的边际成本，并返回新路径的完整信息"""
        if not driver.can_handle_order_group(order_group):
            return float('inf'), {}
        
        # 将新订单组的序列附加到司机现有序列后，形成新的完整路径
        new_sequence = driver.sequence + order_group.sequence
        
        # 基于司机的初始状态，计算新完整路径的总鲁棒成本
        # 此时 compute_rg2_robust_cost 内的 gamma 将基于完整的 new_sequence 计算
        current_stp_time = stp * self.stp_interval
        robust_travel, robust_penalty, new_total_cost, final_pos, final_time = compute_rg2_robust_cost(
            new_sequence, driver.initial_pos, driver.initial_time, -1,
            self.restaurant_coords, self.customer_coords, None,
            self.driver_speed, self.slot_duration, self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0],
            self.orders_data, self.stp_interval, current_stp_time)

        # 成本是边际成本
        marginal_cost = new_total_cost - driver.total_robust_cost

        details = {
            'total_cost': new_total_cost,
            'robust_travel_cost': robust_travel,
            'robust_penalty_cost': robust_penalty,
            'final_pos': final_pos,
            'final_time': final_time,
            'final_sequence': new_sequence
        }
        return marginal_cost, details
    
    def calculate_regret_value(self, dispatch_costs: List[float]) -> float:
        if len(dispatch_costs) < 2: return 0.0
        costs = sorted(dispatch_costs)
        # 遵从论文公式(25)，它是一个2-regret，基于前3个最佳成本
        # sum_{j=1 to 3} (f_nx,j - f_nx,1) = (f_2 - f_1) + (f_3 - f_1)
        # range(1, min(3, len(costs))) -> i = 1, 2
        return sum(costs[i] - costs[0] for i in range(1, min(3, len(costs))))
    
    def initialize_robust_drivers(self, stp: int, initial_states: Dict = None) -> List[RobustDriver]:
        drivers = []
        stp_start_time = stp * self.stp_interval
        
        if initial_states:
            for driver_id, prev_state in initial_states.items():
                start_time = max(prev_state['time'], stp_start_time)
                driver = RobustDriver(driver_id, prev_state['pos'], start_time, self.max_orders_per_driver)
                drivers.append(driver)
        else:
            for driver_id in range(self.num_drivers):
                start_pos = self.optimizer.driver_start_coords[driver_id]
                driver = RobustDriver(driver_id, start_pos, stp_start_time, self.max_orders_per_driver)
                drivers.append(driver)
        return drivers
    
    def assign_robust_order_groups_to_drivers(self, selected_order_groups_by_driver: Dict[int, List[RobustOrderGroup]], stp: int, 
                                            initial_driver_states: Dict = None) -> Tuple[float, List[RobustDriver], Dict]:
        print(f"\n=== 开始Robust OGAH为STP {stp}分配订单组 ===")
        if not selected_order_groups_by_driver:
            final_states = []
            if initial_driver_states:
                for did, state in initial_driver_states.items():
                    driver = RobustDriver(did, state['pos'], max(state['time'], stp * self.optimizer.stp_interval), self.optimizer.max_orders_per_driver)
                    final_states.append(driver)
            return 0.0, final_states, {'assignments': [], 'total_robust_travel_cost': 0, 'total_robust_penalty_cost': 0}

        best_total_cost = float('inf')
        best_final_drivers = None
        best_assignment_details = {}

        # OGAH现在遍历由OGSA为每个delta视角生成的方案
        for delta_perspective, selected_groups in selected_order_groups_by_driver.items():
            print(f"\n--- OGAH: 评估基于视角 delta={delta_perspective} 的方案 ---")
            
            drivers = self.initialize_robust_drivers(stp, initial_driver_states)
            available_drivers = drivers[:]
            unassigned_groups = selected_groups[:]
            
            current_assignments = []
            
            while unassigned_groups and available_drivers:
                og_evaluations = []
                for og in unassigned_groups:
                    driver_costs, driver_details = [], []
                    for driver in available_drivers:
                        cost, details = self.calculate_robust_driver_dispatch_cost(og, driver, stp)
                        driver_costs.append(cost)
                        driver_details.append((driver, details))
                    
                    valid_assigns = [(c, d, dt) for c, (d, dt) in zip(driver_costs, driver_details) if c < float('inf')]
                    if valid_assigns:
                        valid_assigns.sort(key=lambda x: x[0])
                        regret = self.calculate_regret_value([c for c,_,_ in valid_assigns])
                        best_marginal_cost, best_driver, best_details = valid_assigns[0]
                        og_evaluations.append({
                            'og': og, 'regret': regret, 'best_marginal_cost': best_marginal_cost,
                            'best_driver': best_driver, 'best_details': best_details
                        })
                
                if not og_evaluations: break
                
                og_evaluations.sort(key=lambda x: x['regret'], reverse=True)
                best_assign = og_evaluations[0]
                
                sel_og = best_assign['og']
                sel_driver = best_assign['best_driver']
                sel_details = best_assign['best_details']
                
                sel_driver.assign_order_group(sel_og, sel_details)
                
                current_assignments.append({
                    'order_group': sel_og.orders, 'driver_id': sel_driver.driver_id, 
                    'marginal_cost': best_assign['best_marginal_cost'],
                    'new_total_cost': sel_details['total_cost']
                })
                
                unassigned_groups.remove(sel_og)

            if unassigned_groups:
                 print(f"警告: 方案(视角 delta={delta_perspective}) 中, {len(unassigned_groups)}个订单组未能分配")

            # 计算本次方案的总成本
            current_total_cost = sum(d.total_robust_cost for d in drivers)
            print(f"--- OGAH 方案(视角 delta={delta_perspective}) 结束, 总成本: {current_total_cost:.3f} ---")

            if current_total_cost < best_total_cost:
                best_total_cost = current_total_cost
                best_final_drivers = drivers
                
                final_travel = 0
                final_penalty = 0
                for driver in best_final_drivers:
                    if driver.sequence:
                         current_stp_time = driver.initial_time
                         travel_cost, pen, _, _, _ = compute_rg2_robust_cost(
                            driver.sequence, driver.initial_pos, driver.initial_time, -1,
                            self.restaurant_coords, self.customer_coords, None, self.driver_speed, self.slot_duration,
                            self.theta_t, self.travel_time_deviation_ratio, self.travel_cost_per_unit, self.penalty_cost_per_unit,
                            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0], self.orders_data, self.stp_interval, current_stp_time)
                         final_penalty += pen
                         final_travel += travel_cost

                best_assignment_details = {
                    'assignments': current_assignments,
                    'total_robust_travel_cost': final_travel,
                    'total_robust_penalty_cost': final_penalty
                }

        print(f"Robust OGAH完成，最优总成本: {best_total_cost:.3f}")
        return best_total_cost, best_final_drivers, best_assignment_details

class RobustSOCDA:
    """鲁棒单波次订单整合调度算法 (Robust Single Order Consolidation Dispatching Algorithm)"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.robust_oggm = RobustOGGM(food_delivery_optimizer)
        self.robust_ogsa = RobustOGSA(food_delivery_optimizer, self.robust_oggm)
        self.robust_ogah = RobustOGAH(food_delivery_optimizer)
        
        print("Robust SOCDA (扩展案例) 初始化完成")
    
    def solve_single_stp_robust_dispatch(self, orders_in_stp: List[int], stp: int, 
                                         initial_driver_states: Dict) -> Tuple[float, float, List[RobustDriver], Dict]:
        print(f"\n{'='*60}")
        print(f"Robust SOCDA求解STP {stp}鲁棒调度问题, 订单: {orders_in_stp}")
        
        if not orders_in_stp:
            final_drivers = []
            if initial_driver_states:
                for did, state in initial_driver_states.items():
                    driver = RobustDriver(did, state['pos'], max(state['time'], stp * self.optimizer.stp_interval), self.optimizer.max_orders_per_driver)
                    final_drivers.append(driver)
            return 0.0, 0.0, final_drivers, {'message': 'No orders'}
        
        # Step 1: OGGM
        robust_order_groups = self.robust_oggm.generate_robust_order_groups_for_stp(
            orders_in_stp, stp, initial_driver_states)
        if not robust_order_groups: 
            return 0.0, 0.0, self.robust_ogah.initialize_robust_drivers(stp, initial_driver_states), {}
            
        # Step 2: OGSA - 现在返回一个包含多个方案的字典
        # {delta_perspective: [selected_groups]}
        selected_groups_by_driver_perspective = self.robust_ogsa.select_robust_order_groups_for_stp(
            robust_order_groups, orders_in_stp, stp, initial_driver_states)
        if not selected_groups_by_driver_perspective: 
            return 0.0, 0.0, self.robust_ogah.initialize_robust_drivers(stp, initial_driver_states), {}

        # Step 3: OGAH - 现在接收多个方案并从中择优
        total_cost, final_drivers, details = self.robust_ogah.assign_robust_order_groups_to_drivers(
            selected_groups_by_driver_perspective, stp, initial_driver_states)
            
        travel_cost = details.get('total_robust_travel_cost', 0)
        penalty_cost = details.get('total_robust_penalty_cost', 0)

        print(f"\nSTP {stp} 求解完成: 总成本={total_cost:.2f}")
        return travel_cost, penalty_cost, final_drivers, details

def test_complete_robust_socda():
    """完整测试Robust SOCDA扩展案例"""
    print("="*80)
    print("Robust SOCDA扩展案例测试")
    print("="*80)
    
    class MockRobustFoodDeliveryOptimizer:
        def __init__(self):
            # --- 与 test3.py 同步的参数 ---
            self.commission_rate = 0.18
            self.travel_cost_per_unit = 0.2  
            self.penalty_cost_per_unit = 1.0
            self.driver_speed = 1.0
            self.max_orders_per_driver = 4
            self.num_drivers = 2
            self.stps = list(range(6))  # 0 to 5
            self.stp_interval = 10
            self.slot_duration = 30 # Not directly used in cost, but kept for context

            self.restaurant_coords = {
                0: (2, 2),
                1: (8, 2)
            }
            self.driver_start_coords = {
                0: (5, 1),
                1: (6, 1)
            }
            # --- 与 test3.py 同步的订单数据 ---
            self.orders_data = {
                # STP 0 (0分钟)
                0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0},
                1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'stp': 0},
                2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'stp': 0},
                # STP 1 (10分钟)
                3: {'value': 32, 'restaurant_id': 1, 'customer_coord': (7, 5), 'est_delivery': 14, 'stp': 1},
                4: {'value': 26, 'restaurant_id': 0, 'customer_coord': (2, 7), 'est_delivery': 16, 'stp': 1},
                5: {'value': 35, 'restaurant_id': 1, 'customer_coord': (8, 6), 'est_delivery': 13, 'stp': 1},
                # STP 2 (20分钟)
                6: {'value': 29, 'restaurant_id': 0, 'customer_coord': (4, 5), 'est_delivery': 15, 'stp': 2},
                7: {'value': 33, 'restaurant_id': 1, 'customer_coord': (6, 7), 'est_delivery': 17, 'stp': 2},
                8: {'value': 27, 'restaurant_id': 0, 'customer_coord': (1, 8), 'est_delivery': 20, 'stp': 2},
                # STP 3 (30分钟)
                9: {'value': 31, 'restaurant_id': 1, 'customer_coord': (9, 7), 'est_delivery': 16, 'stp': 3},
                10: {'value': 24, 'restaurant_id': 0, 'customer_coord': (3, 4), 'est_delivery': 14, 'stp': 3},
                11: {'value': 36, 'restaurant_id': 1, 'customer_coord': (7, 8), 'est_delivery': 18, 'stp': 3},
                # STP 4 (40分钟)
                12: {'value': 28, 'restaurant_id': 0, 'customer_coord': (2, 6), 'est_delivery': 15, 'stp': 4},
                13: {'value': 34, 'restaurant_id': 1, 'customer_coord': (8, 5), 'est_delivery': 13, 'stp': 4},
                14: {'value': 30, 'restaurant_id': 0, 'customer_coord': (4, 7), 'est_delivery': 17, 'stp': 4},
                # STP 5 (50分钟)
                15: {'value': 32, 'restaurant_id': 1, 'customer_coord': (6, 8), 'est_delivery': 16, 'stp': 5},
                16: {'value': 26, 'restaurant_id': 0, 'customer_coord': (1, 6), 'est_delivery': 18, 'stp': 5},
                17: {'value': 37, 'restaurant_id': 1, 'customer_coord': (9, 6), 'est_delivery': 14, 'stp': 5}
            }

    mock_optimizer = MockRobustFoodDeliveryOptimizer()
    robust_socda = RobustSOCDA(mock_optimizer)
        
    pricing_schemes = [(3, 3), (3, 6), (6, 3), (6, 6)]
    all_results = []
        
    # 简化版价格判定逻辑 -> 与test3.py的get_time_slot_for_stp对齐
    for price_slot1, price_slot2 in pricing_schemes:
        print(f"\n{'='*70}\n测试鲁棒定价方案: (时段1={price_slot1}, 时段2={price_slot2})\n{'='*70}")
        
        driver_states = {
            k: {'pos': mock_optimizer.driver_start_coords[k], 'time': 0.0}
            for k in range(mock_optimizer.num_drivers)
        }
        
        total_travel = 0.0
        total_penalty = 0.0

        for stp in mock_optimizer.stps:
            stp_orders = [oid for oid, data in mock_optimizer.orders_data.items() if data['stp'] == stp]
            
            # 更新司机时间
            for k in driver_states:
                 driver_states[k]['time'] = max(driver_states[k]['time'], stp * mock_optimizer.stp_interval)

            travel, penalty, final_drivers, _ = robust_socda.solve_single_stp_robust_dispatch(
                stp_orders, stp, driver_states)
            
            total_travel += travel
            total_penalty += penalty
            
            # 更新司机状态
            for driver in final_drivers:
                driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
        
        # 计算收入和利润
        def get_price(stp_index, p1, p2):
            return p1 if stp_index * mock_optimizer.stp_interval < 30 else p2
        
        total_value = sum(d['value'] for d in mock_optimizer.orders_data.values())
        commission = total_value * mock_optimizer.commission_rate
        delivery_fees = sum(get_price(data['stp'], price_slot1, price_slot2) for data in mock_optimizer.orders_data.values())
        revenue = commission + delivery_fees
        
        total_cost = total_travel + total_penalty
        profit = revenue - total_cost
            
        result = {
            'scheme': (price_slot1, price_slot2), 'revenue': revenue,
            'robust_travel_cost': total_travel, 'robust_penalty_cost': total_penalty,
            'total_robust_cost': total_cost, 'robust_profit': profit
            }
        all_results.append(result)
            
        print(f"\n--- 方案({price_slot1}, {price_slot2})汇总 ---")
        print(f"总收入: {revenue:.2f}, 总成本: {total_cost:.2f}, 总利润: {profit:.2f}")
        
    print(f"\n{'='*80}\n所有鲁棒定价方案结果汇总对比\n{'='*80}")
    print(f"{'方案':<12} {'收入':<10} {'旅行成本':<12} {'延迟成本':<12} {'总成本':<12} {'利润':<10} {'最优':<6}")
    print("-" * 85)
    best_res = max(all_results, key=lambda x: x['robust_profit'])
    for res in all_results:
        scheme_str = f"({res['scheme'][0]}, {res['scheme'][1]})"
        is_best = "★" if res == best_res else ""
        print(f"{scheme_str:<12} {res['revenue']:<10.2f} {res['robust_travel_cost']:<12.2f} "
              f"{res['robust_penalty_cost']:<12.2f} {res['total_robust_cost']:<12.2f} "
              f"{res['robust_profit']:<10.2f} {is_best:<6}")
        
    return robust_socda, all_results

def main():
    test_complete_robust_socda()

if __name__ == "__main__":
    main()