import sys
import os

# 添加当前目录到路径
sys.path.append(os.getcwd())

def test_delta_sensitivity():
    """测试两个实现中delta参数的敏感性差异"""
    print("="*80)
    print("Delta参数敏感性对比测试")
    print("="*80)
    
    # 测试 Robust SOCDA 5.py 的实现
    print("\n1. 测试 Robust SOCDA 5.py (delta=1)")
    try:
        # 导入并运行 RS5 的测试
        from importlib import import_module
        rs5_module = import_module("Robust SOCDA 5")
        
        # 创建mock优化器
        class MockOptimizer:
            def __init__(self):
                self.commission_rate = 0.18
                self.travel_cost_per_unit = 0.2  
                self.penalty_cost_per_unit = 1.0
                self.driver_speed = 1.0
                self.max_orders_per_driver = 4
                self.num_drivers = 2
                self.stps = list(range(6))
                self.stp_interval = 10
                self.slot_duration = 30
                self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
                self.driver_start_coords = {0: (5, 1), 1: (6, 1)}
                self.orders_data = {
                    0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0},
                    1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'stp': 0},
                    2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'stp': 0},
                    3: {'value': 32, 'restaurant_id': 1, 'customer_coord': (7, 5), 'est_delivery': 14, 'stp': 1},
                    4: {'value': 26, 'restaurant_id': 0, 'customer_coord': (2, 7), 'est_delivery': 16, 'stp': 1},
                    5: {'value': 35, 'restaurant_id': 1, 'customer_coord': (8, 6), 'est_delivery': 13, 'stp': 1},
                }
        
        mock_opt = MockOptimizer()
        
        # 测试delta=1的情况
        robust_oggm_1 = rs5_module.RobustOGGM(mock_opt)
        robust_oggm_1.delta_for_virtual_driver = 1
        
        # 模拟司机状态
        driver_states = {
            0: {'pos': (5, 1), 'time': 0.0},
            1: {'pos': (6, 1), 'time': 0.0}
        }
        
        # 生成订单组并计算成本
        orders_stp0 = [0, 1, 2]
        order_groups_1 = robust_oggm_1.generate_robust_order_groups_for_stp(orders_stp0, 0, driver_states)
        
        total_cost_delta1 = sum(og.robust_cost for og in order_groups_1)
        print(f"RS5 delta=1 总成本: {total_cost_delta1:.2f}")
        
        # 测试delta=2的情况
        robust_oggm_2 = rs5_module.RobustOGGM(mock_opt)
        robust_oggm_2.delta_for_virtual_driver = 2
        
        order_groups_2 = robust_oggm_2.generate_robust_order_groups_for_stp(orders_stp0, 0, driver_states)
        total_cost_delta2 = sum(og.robust_cost for og in order_groups_2)
        print(f"RS5 delta=2 总成本: {total_cost_delta2:.2f}")
        
        rs5_increase = total_cost_delta2 - total_cost_delta1
        rs5_increase_pct = (rs5_increase / total_cost_delta1) * 100 if total_cost_delta1 > 0 else 0
        print(f"RS5 成本增加: {rs5_increase:.2f} ({rs5_increase_pct:.1f}%)")
        
    except Exception as e:
        print(f"RS5测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*50)
    
    # 测试 Robust Hybrid 2.py 的实现
    print("\n2. 测试 Robust Hybrid 2.py")
    try:
        rh2_module = import_module("Robust Hybrid 2")
        
        class MockOptimizer2:
            def __init__(self):
                self.travel_cost_per_unit = 0.2
                self.penalty_cost_per_unit = 1.0
                self.driver_speed = 1.0
                self.max_orders_per_driver = 4
                self.num_drivers = 2
                self.stps = list(range(6))
                self.stp_interval = 10
                self.commission_rate = 0.18
                self.theta_t = 0.3
                self.travel_time_deviation_ratio = 0.2
                self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
                self.driver_start_coords = {0: (5, 1), 1: (6, 1)}
                self.orders_data = {
                    0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'stp': 0},
                    1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'stp': 0},
                    2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'stp': 0},
                    3: {'value': 32, 'restaurant_id': 1, 'customer_coord': (7, 5), 'est_delivery': 14, 'stp': 1},
                    4: {'value': 26, 'restaurant_id': 0, 'customer_coord': (2, 7), 'est_delivery': 16, 'stp': 1},
                    5: {'value': 35, 'restaurant_id': 1, 'customer_coord': (8, 6), 'est_delivery': 13, 'stp': 1},
                }
        
        mock_opt2 = MockOptimizer2()
        
        # 测试delta=1的情况
        oggm_1 = rh2_module.OGGM(mock_opt2)
        oggm_1.delta_for_virtual_driver = 1
        
        driver_states = {
            0: {'pos': (5, 1), 'time': 0.0},
            1: {'pos': (6, 1), 'time': 0.0}
        }
        
        orders_stp0 = [0, 1, 2]
        order_groups_1 = oggm_1.generate_order_groups_for_stp(orders_stp0, 0, driver_states)
        
        total_cost_delta1 = sum(og.robust_cost for og in order_groups_1)
        print(f"RH2 delta=1 总成本: {total_cost_delta1:.2f}")
        
        # 测试delta=2的情况
        oggm_2 = rh2_module.OGGM(mock_opt2)
        oggm_2.delta_for_virtual_driver = 2
        
        order_groups_2 = oggm_2.generate_order_groups_for_stp(orders_stp0, 0, driver_states)
        total_cost_delta2 = sum(og.robust_cost for og in order_groups_2)
        print(f"RH2 delta=2 总成本: {total_cost_delta2:.2f}")
        
        rh2_increase = total_cost_delta2 - total_cost_delta1
        rh2_increase_pct = (rh2_increase / total_cost_delta1) * 100 if total_cost_delta1 > 0 else 0
        print(f"RH2 成本增加: {rh2_increase:.2f} ({rh2_increase_pct:.1f}%)")
        
    except Exception as e:
        print(f"RH2测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*80)
    print("对比总结:")
    print("="*80)

if __name__ == "__main__":
    test_delta_sensitivity()
